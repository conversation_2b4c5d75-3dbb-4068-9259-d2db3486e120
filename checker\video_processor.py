import os
import json
import re
import shutil
import csv
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import math

class VideoProcessor:
    def __init__(self, analysis_file: str, video_dir: str, best_video_dir: str = "Best_video", prompts_file: str = "../drone_prompts.csv"):
        self.analysis_file = analysis_file
        self.video_dir = video_dir
        self.best_video_dir = best_video_dir
        self.prompts_file = prompts_file
        self.state_file = "checker/evaluation_state.json"
        self.log_file = "checker/evaluation_log.json"

        # 确保目录存在
        os.makedirs("checker", exist_ok=True)
        os.makedirs(best_video_dir, exist_ok=True)

        # 加载或初始化状态
        self.state = self.load_state()
        self.prefix_groups = self.load_prefix_groups()
        self.prompts = self.load_prompts()
    
    def load_prefix_groups(self) -> Dict:
        """从分析文件加载前缀组并修复前缀匹配问题"""
        raw_prefix_groups = {}

        if not os.path.exists(self.analysis_file):
            print(f"分析文件不存在: {self.analysis_file}")
            return raw_prefix_groups

        try:
            with open(self.analysis_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 第一步：解析原始前缀组
            current_prefix = None
            current_videos = []

            for line in content.split('\n'):
                original_line = line
                line = line.strip()
                if line.startswith('前缀: '):
                    # 保存上一个前缀组
                    if current_prefix and current_videos:
                        raw_prefix_groups[current_prefix] = current_videos.copy()

                    # 开始新的前缀组
                    current_prefix = line[4:]  # 去掉"前缀: "
                    current_videos = []
                elif original_line.startswith('  - '):
                    # 添加视频文件（使用原始行检查格式）
                    video_file = original_line[4:].strip()  # 去掉"  - "并清理
                    if current_prefix is not None:  # 确保有当前前缀
                        current_videos.append(video_file)

            # 保存最后一个前缀组
            if current_prefix and current_videos:
                raw_prefix_groups[current_prefix] = current_videos.copy()

            print(f"原始解析得到 {len(raw_prefix_groups)} 个前缀组")

            # 第二步：修复前缀匹配问题，合并应该属于同一组的视频
            fixed_prefix_groups = self.fix_prefix_matching(raw_prefix_groups)

            print(f"修复后得到 {len(fixed_prefix_groups)} 个前缀组")
            return fixed_prefix_groups

        except Exception as e:
            print(f"加载前缀组时出错: {e}")
            return {}

    def fix_prefix_matching(self, raw_prefix_groups: Dict) -> Dict:
        """修复前缀匹配问题，合并应该属于同一组的视频"""
        fixed_groups = {}

        # 收集所有视频文件，按照真实前缀分组
        all_videos = []
        for prefix, videos in raw_prefix_groups.items():
            all_videos.extend(videos)

        # 按照视频文件名提取真实前缀
        video_to_real_prefix = {}
        for video in all_videos:
            real_prefix = self.extract_real_prefix(video)
            video_to_real_prefix[video] = real_prefix

        # 按真实前缀重新分组
        for video, real_prefix in video_to_real_prefix.items():
            if real_prefix not in fixed_groups:
                fixed_groups[real_prefix] = []
            fixed_groups[real_prefix].append(video)

        # 按视频文件名排序，确保一致性
        for prefix in fixed_groups:
            fixed_groups[prefix].sort()

        return fixed_groups

    def extract_real_prefix(self, video_filename: str) -> str:
        """从视频文件名提取真实前缀"""
        # 视频文件名格式：{prefix}_{number}_results_seed{seed}_stage2.mp4
        # 我们需要提取到最后一个数字标识符之前的部分

        # 移除文件扩展名
        name_without_ext = video_filename.replace('.mp4', '')

        # 移除结尾的 _results_seed{number}_stage2 部分
        if '_results_seed' in name_without_ext:
            base_name = name_without_ext.split('_results_seed')[0]
        else:
            base_name = name_without_ext

        # 分割成部分
        parts = base_name.split('_')

        # 找到最后一个数字部分的位置
        # 通常格式是：frame{num}_from_{id}_{dataset}_train_{content}_{num1}_{num2}_{num3}
        # 我们需要保留到倒数第一个数字之前

        if len(parts) < 2:
            return base_name

        # 从后往前找，移除最后一个纯数字部分
        result_parts = parts[:]

        # 移除最后一个部分如果它是纯数字
        if result_parts and result_parts[-1].isdigit():
            result_parts = result_parts[:-1]

        # 重新组合
        real_prefix = '_'.join(result_parts)

        return real_prefix

    def load_prompts(self) -> Dict[str, str]:
        """从CSV文件加载提示信息"""
        prompts = {}

        if not os.path.exists(self.prompts_file):
            print(f"提示文件不存在: {self.prompts_file}")
            return prompts

        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']

            for encoding in encodings:
                try:
                    with open(self.prompts_file, 'r', encoding=encoding) as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            image_id = row.get('image_id', '').strip()
                            base_prompt = row.get('base_prompt', '').strip()
                            if image_id and base_prompt:
                                prompts[image_id] = base_prompt
                    break  # 成功读取，跳出循环
                except UnicodeDecodeError:
                    continue  # 尝试下一个编码

            print(f"成功加载 {len(prompts)} 个提示信息")
            return prompts

        except Exception as e:
            print(f"加载提示信息时出错: {e}")
            return {}

    def get_prompt_for_prefix(self, prefix: str) -> str:
        """获取指定前缀的提示信息"""
        # 直接查找完全匹配的前缀
        if prefix in self.prompts:
            return self.prompts[prefix]

        # 如果没有找到，尝试查找部分匹配
        for image_id in self.prompts:
            if prefix.startswith(image_id) or image_id.startswith(prefix):
                return self.prompts[image_id]

        return "No prompt available for this video sequence."
    
    def load_state(self) -> Dict:
        """加载评估状态"""
        if os.path.exists(self.state_file):
            with open(self.state_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return {
            "current_prefix_index": 0,
            "completed_prefixes": [],
            "current_tournament": None,
            "total_prefixes": 0,
            "start_time": datetime.now().isoformat()
        }
    
    def save_state(self):
        """保存评估状态"""
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(self.state, f, ensure_ascii=False, indent=2)
    
    def log_action(self, action: str, data: Dict):
        """记录评估操作"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "data": data
        }
        
        # 加载现有日志
        logs = []
        if os.path.exists(self.log_file):
            with open(self.log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
        
        logs.append(log_entry)
        
        # 保存日志
        with open(self.log_file, 'w', encoding='utf-8') as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)
    
    def create_tournament(self, prefix: str, videos: List[str]) -> Dict:
        """为给定前缀创建淘汰赛结构"""
        if len(videos) == 0:
            return None
        
        if len(videos) == 1:
            # 单个视频，直接获胜
            tournament = {
                "prefix": prefix,
                "videos": videos,
                "status": "single_video",
                "final_winner": videos[0],
                "rounds": [],
                "created_at": datetime.now().isoformat()
            }
            self.log_action("single_video_auto_win", {
                "prefix": prefix,
                "winner": videos[0]
            })
            return tournament
        
        # 创建多轮淘汰赛
        tournament = {
            "prefix": prefix,
            "videos": videos,
            "status": "pending",
            "current_round": 1,
            "current_group": 0,
            "rounds": [],
            "final_winner": None,
            "created_at": datetime.now().isoformat()
        }
        
        # 计算需要的轮数
        total_rounds = self.calculate_rounds_needed(len(videos))
        
        # 创建第一轮分组
        first_round_groups = self.create_groups(videos, 4)
        tournament["rounds"].append({
            "round_number": 1,
            "groups": first_round_groups,
            "completed": False
        })
        
        return tournament
    
    def calculate_rounds_needed(self, video_count: int) -> int:
        """计算需要的轮数"""
        if video_count <= 1:
            return 0
        if video_count <= 4:
            return 1
        
        rounds = 1
        remaining = math.ceil(video_count / 4)
        
        while remaining > 4:
            rounds += 1
            remaining = math.ceil(remaining / 4)
        
        if remaining > 1:
            rounds += 1
            
        return min(rounds, 5)  # 最多5轮
    
    def create_groups(self, videos: List[str], group_size: int) -> List[Dict]:
        """将视频分组"""
        groups = []
        for i in range(0, len(videos), group_size):
            group_videos = videos[i:i + group_size]
            groups.append({
                "group_id": len(groups) + 1,
                "videos": group_videos,
                "winner": None,
                "completed": False
            })
        return groups
    
    def get_next_comparison(self) -> Optional[Dict]:
        """获取下一个需要比较的视频组"""
        # 更新总前缀数
        self.state["total_prefixes"] = len(self.prefix_groups)
        
        # 检查是否有当前进行中的锦标赛
        if self.state.get("current_tournament"):
            tournament = self.state["current_tournament"]
            
            # 查找下一个未完成的组
            current_round = tournament["rounds"][tournament["current_round"] - 1]
            
            for group in current_round["groups"]:
                if not group["completed"]:
                    return {
                        "type": "comparison",
                        "prefix": tournament["prefix"],
                        "prompt": self.get_prompt_for_prefix(tournament["prefix"]),
                        "round": tournament["current_round"],
                        "group": group["group_id"],
                        "videos": group["videos"],
                        "progress": {
                            "current_prefix": self.state["current_prefix_index"] + 1,
                            "total_prefixes": self.state["total_prefixes"],
                            "current_round": tournament["current_round"],
                            "current_group": group["group_id"],
                            "total_groups": len(current_round["groups"])
                        }
                    }
        
        # 没有进行中的锦标赛，开始下一个前缀
        return self.start_next_prefix()
    
    def start_next_prefix(self) -> Optional[Dict]:
        """开始下一个前缀的评估"""
        prefix_list = list(self.prefix_groups.keys())
        
        if self.state["current_prefix_index"] >= len(prefix_list):
            # 所有前缀都已完成
            return {
                "type": "completed",
                "message": "所有视频评估已完成！",
                "total_completed": len(self.state["completed_prefixes"]),
                "total_prefixes": len(prefix_list)
            }
        
        current_prefix = prefix_list[self.state["current_prefix_index"]]
        videos = self.prefix_groups[current_prefix]
        
        # 创建新的锦标赛
        tournament = self.create_tournament(current_prefix, videos)
        
        if not tournament:
            # 跳过这个前缀
            self.state["current_prefix_index"] += 1
            return self.start_next_prefix()
        
        self.state["current_tournament"] = tournament
        
        if tournament["status"] == "single_video":
            # 单个视频，自动完成
            self.complete_tournament(tournament["final_winner"])
            return self.start_next_prefix()
        
        self.save_state()
        return self.get_next_comparison()
    
    def submit_winner(self, winner_video: str) -> Dict:
        """提交获胜视频"""
        if not self.state.get("current_tournament"):
            return {"error": "没有进行中的锦标赛"}
        
        tournament = self.state["current_tournament"]
        current_round = tournament["rounds"][tournament["current_round"] - 1]
        
        # 找到当前组并标记获胜者
        for group in current_round["groups"]:
            if not group["completed"] and winner_video in group["videos"]:
                group["winner"] = winner_video
                group["completed"] = True
                
                self.log_action("group_winner_selected", {
                    "prefix": tournament["prefix"],
                    "round": tournament["current_round"],
                    "group": group["group_id"],
                    "videos": group["videos"],
                    "winner": winner_video
                })
                break
        
        # 检查当前轮是否完成
        if all(group["completed"] for group in current_round["groups"]):
            current_round["completed"] = True
            winners = [group["winner"] for group in current_round["groups"]]
            
            if len(winners) == 1:
                # 锦标赛完成
                self.complete_tournament(winners[0])
            else:
                # 需要下一轮
                self.start_next_round(winners)
        
        self.save_state()
        return {"success": True}
    
    def start_next_round(self, winners: List[str]):
        """开始下一轮"""
        tournament = self.state["current_tournament"]
        tournament["current_round"] += 1
        
        next_round_groups = self.create_groups(winners, 4)
        tournament["rounds"].append({
            "round_number": tournament["current_round"],
            "groups": next_round_groups,
            "completed": False
        })
    
    def complete_tournament(self, final_winner: str):
        """完成锦标赛"""
        tournament = self.state["current_tournament"]
        tournament["final_winner"] = final_winner
        tournament["status"] = "completed"
        tournament["completed_at"] = datetime.now().isoformat()
        
        # 复制获胜视频到Best_video目录
        self.copy_winner_video(tournament["prefix"], final_winner)
        
        # 记录完成的前缀
        self.state["completed_prefixes"].append({
            "prefix": tournament["prefix"],
            "winner": final_winner,
            "completed_at": tournament["completed_at"]
        })
        
        # 移动到下一个前缀
        self.state["current_prefix_index"] += 1
        self.state["current_tournament"] = None
        
        self.log_action("tournament_completed", {
            "prefix": tournament["prefix"],
            "final_winner": final_winner,
            "total_rounds": len(tournament["rounds"])
        })
    
    def copy_winner_video(self, prefix: str, winner_video: str):
        """复制获胜视频到Best_video目录"""
        source_path = os.path.join(self.video_dir, winner_video)
        
        # 创建新的文件名，包含前缀信息
        base_name = os.path.splitext(winner_video)[0]
        extension = os.path.splitext(winner_video)[1]
        new_name = f"{prefix}_BEST{extension}"
        
        dest_path = os.path.join(self.best_video_dir, new_name)
        
        try:
            shutil.copy2(source_path, dest_path)
            self.log_action("video_copied", {
                "source": source_path,
                "destination": dest_path,
                "prefix": prefix,
                "original_name": winner_video
            })
        except Exception as e:
            self.log_action("copy_error", {
                "error": str(e),
                "source": source_path,
                "destination": dest_path
            })
    
    def get_status(self) -> Dict:
        """获取当前状态"""
        total_prefixes = len(self.prefix_groups)
        completed = len(self.state["completed_prefixes"])
        
        status = {
            "total_prefixes": total_prefixes,
            "completed_prefixes": completed,
            "current_prefix_index": self.state["current_prefix_index"],
            "progress_percentage": (completed / total_prefixes * 100) if total_prefixes > 0 else 0,
            "start_time": self.state.get("start_time"),
            "current_tournament": self.state.get("current_tournament")
        }
        
        if self.state.get("current_tournament"):
            tournament = self.state["current_tournament"]
            status["current_prefix"] = tournament["prefix"]
            status["current_round"] = tournament.get("current_round", 1)
            
            if tournament["rounds"]:
                current_round = tournament["rounds"][tournament["current_round"] - 1]
                status["total_groups_in_round"] = len(current_round["groups"])
                status["completed_groups_in_round"] = sum(1 for g in current_round["groups"] if g["completed"])
        
        return status

    def reset_current_tournament(self) -> Dict:
        """重置当前锦标赛，保留已完成的前缀评估"""
        if not self.state.get("current_tournament"):
            return {"message": "没有进行中的锦标赛需要重置"}

        current_tournament = self.state["current_tournament"]
        prefix = current_tournament["prefix"]

        # 记录重置操作
        self.log_action("tournament_reset", {
            "prefix": prefix,
            "reset_at_round": current_tournament.get("current_round", 1),
            "reason": "user_requested"
        })

        # 清除当前锦标赛状态
        self.state["current_tournament"] = None

        # 保存状态
        self.save_state()

        return {
            "message": f"已重置前缀 {prefix} 的评估",
            "reset_prefix": prefix
        }
