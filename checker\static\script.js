class VideoEvaluator {
    constructor() {
        this.currentVideos = [];
        this.isLoading = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadNextRound();
        this.updateStatus();

        // 定期更新状态
        setInterval(() => this.updateStatus(), 5000);
    }

    bindEvents() {
        // 选择按钮事件
        document.querySelectorAll('.select-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const videoIndex = parseInt(e.target.dataset.videoIndex);
                this.selectWinner(videoIndex);
            });
        });

        // 控制按钮事件
        document.getElementById('playAllBtn').addEventListener('click', () => this.playAllVideos());
        document.getElementById('pauseAllBtn').addEventListener('click', () => this.pauseAllVideos());
        document.getElementById('skipBtn').addEventListener('click', () => this.showConfirmModal('跳过当前前缀？', () => this.skipPrefix()));
        document.getElementById('resetBtn').addEventListener('click', () => this.showConfirmModal('重置当前前缀的评估？', () => this.resetCurrentRound()));
        document.getElementById('exportBtn').addEventListener('click', () => this.exportResults());

        // 模态框事件
        document.getElementById('confirmYes').addEventListener('click', () => this.executeConfirmedAction());
        document.getElementById('confirmNo').addEventListener('click', () => this.hideConfirmModal());

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // 防止在输入框中触发快捷键
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            if (e.key >= '1' && e.key <= '4') {
                e.preventDefault();
                const index = parseInt(e.key) - 1;
                const videoItem = document.querySelector(`[data-video-index="${index}"]`);
                if (this.currentVideos[index] && videoItem && !videoItem.classList.contains('hidden') && !videoItem.classList.contains('disabled')) {
                    this.selectWinner(index);
                }
            } else if (e.key === ' ') {
                e.preventDefault();
                this.toggleAllVideos();
            } else if (e.key === 'r' || e.key === 'R') {
                e.preventDefault();
                this.playAllVideos();
            } else if (e.key === 's' || e.key === 'S') {
                e.preventDefault();
                this.pauseAllVideos();
            } else if (e.key === 'n' || e.key === 'N') {
                e.preventDefault();
                this.skipPrefix();
            }
        });
    }

    async loadNextRound() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading(true);

        try {
            const response = await fetch('/api/next_round');
            const result = await response.json();

            if (result.success) {
                if (result.data.type === 'comparison') {
                    this.displayComparison(result.data);
                } else if (result.data.type === 'completed') {
                    this.showCompletion(result.data);
                }
            } else {
                this.showError('加载失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    displayComparison(data) {
        this.currentVideos = data.videos;

        // 更新前缀信息
        document.getElementById('currentPrefix').textContent = `当前前缀: ${data.prefix}`;
        document.getElementById('roundInfo').textContent =
            `第 ${data.round} 轮 - 第 ${data.group} 组 (共 ${data.progress.total_groups} 组)`;

        // 显示提示信息
        this.displayPrompt(data.prompt);

        // 更新进度
        this.updateProgress(data.progress);

        // 设置动态网格布局
        const videoGrid = document.getElementById('videoGrid');
        const videoCount = data.videos.length;

        // 移除所有网格类
        videoGrid.classList.remove('grid-1', 'grid-2', 'grid-3', 'grid-4');

        // 添加对应的网格类
        videoGrid.classList.add(`grid-${videoCount}`);

        // 加载视频和设置可见性
        for (let index = 0; index < 4; index++) {
            const videoElement = document.getElementById(`video${index}`);
            const nameElement = document.getElementById(`videoName${index}`);
            const itemElement = document.querySelector(`[data-video-index="${index}"]`);
            const selectBtn = itemElement.querySelector('.select-btn');

            if (index < videoCount && data.videos[index]) {
                const video = data.videos[index];

                // 设置视频源和信息
                videoElement.src = `/video/${video}`;
                nameElement.textContent = this.formatVideoName(video);

                // 显示视频项
                itemElement.classList.remove('hidden', 'disabled');

                // 重置按钮状态
                this.resetButtonState(selectBtn);

                // 预加载视频
                videoElement.load();

                // 添加视频加载事件监听
                videoElement.addEventListener('loadeddata', () => {
                    console.log(`视频 ${index + 1} 加载完成`);
                });

                videoElement.addEventListener('error', (e) => {
                    console.error(`视频 ${index + 1} 加载失败:`, e);
                    nameElement.textContent = `${this.formatVideoName(video)} (加载失败)`;
                    itemElement.classList.add('disabled');
                });

            } else {
                // 隐藏未使用的视频项
                itemElement.classList.add('hidden');
                videoElement.src = '';
            }
        }

        // 特殊情况处理
        if (videoCount === 1) {
            this.showSingleVideoMessage(data.videos[0]);
        }

        // 显示视频网格，隐藏其他面板
        videoGrid.style.display = 'grid';
        document.getElementById('completionPanel').style.display = 'none';
    }

    formatVideoName(filename) {
        // 简化视频文件名显示
        const parts = filename.split('_');
        if (parts.length > 3) {
            return `${parts[0]}_${parts[1]}_...${parts[parts.length - 1]}`;
        }
        return filename;
    }

    showSingleVideoMessage(video) {
        // 为单个视频显示特殊消息
        const message = document.createElement('div');
        message.className = 'single-video-message';
        message.innerHTML = `
            <h3>🎯 单个视频自动获胜</h3>
            <p>该前缀只有一个视频，已自动标记为最佳视频</p>
            <p><strong>${this.formatVideoName(video)}</strong></p>
        `;
        message.style.cssText = `
            background: rgba(39, 174, 96, 0.1);
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            color: #27ae60;
        `;

        // 插入到视频网格前
        const videoGrid = document.getElementById('videoGrid');
        const existingMessage = document.querySelector('.single-video-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        videoGrid.parentNode.insertBefore(message, videoGrid);

        // 3秒后自动移除消息
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 3000);
    }

    displayPrompt(prompt) {
        const promptPanel = document.getElementById('promptPanel');
        const promptText = document.getElementById('promptText');

        if (prompt && prompt !== "No prompt available for this video sequence.") {
            promptText.textContent = prompt;
            promptPanel.style.display = 'block';
        } else {
            promptText.textContent = "No prompt available for this video sequence.";
            promptPanel.style.display = 'block';
        }
    }

    async selectWinner(videoIndex) {
        if (this.isLoading || !this.currentVideos[videoIndex]) return;

        const winnerVideo = this.currentVideos[videoIndex];

        // 视觉反馈
        const selectedBtn = document.querySelector(`[data-video-index="${videoIndex}"] .select-btn`);
        selectedBtn.style.background = '#f39c12';
        selectedBtn.textContent = '选择中...';

        try {
            const response = await fetch('/api/select_winner', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ winner: winnerVideo })
            });

            const result = await response.json();

            if (result.success) {
                // 显示成功反馈
                selectedBtn.style.background = '#27ae60';
                selectedBtn.textContent = '已选择 ✓';

                // 短暂延迟后加载下一轮
                setTimeout(() => {
                    this.loadNextRound();
                }, 1000);
            } else {
                this.showError('提交失败: ' + result.error);
                this.resetButtonState(selectedBtn);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
            this.resetButtonState(selectedBtn);
        }
    }

    resetButtonState(button) {
        if (button) {
            button.style.background = '#27ae60';
            button.textContent = '选择此视频';
            button.disabled = false;
        }
    }

    async updateStatus() {
        try {
            const response = await fetch('/api/status');
            const result = await response.json();

            if (result.success) {
                const status = result.data;
                this.updateProgress({
                    current_prefix: status.current_prefix_index + 1,
                    total_prefixes: status.total_prefixes
                });
            }
        } catch (error) {
            console.error('状态更新失败:', error);
        }
    }

    updateProgress(progress) {
        const percentage = (progress.current_prefix / progress.total_prefixes) * 100;
        document.getElementById('progressFill').style.width = `${percentage}%`;
        document.getElementById('progressText').textContent =
            `进度: ${progress.current_prefix}/${progress.total_prefixes} (${percentage.toFixed(1)}%)`;
    }

    showCompletion(data) {
        document.getElementById('videoGrid').style.display = 'none';
        document.getElementById('completionPanel').style.display = 'block';
        document.getElementById('completionMessage').textContent = data.message;
        document.getElementById('totalProcessed').textContent = data.total_completed;

        // 更新进度为100%
        document.getElementById('progressFill').style.width = '100%';
        document.getElementById('progressText').textContent = '评估完成！';
    }

    playAllVideos() {
        document.querySelectorAll('video').forEach(video => {
            if (video.src && !video.paused) return;
            video.play().catch(e => console.log('播放失败:', e));
        });
    }

    pauseAllVideos() {
        document.querySelectorAll('video').forEach(video => {
            video.pause();
        });
    }

    toggleAllVideos() {
        const videos = document.querySelectorAll('video');
        const anyPlaying = Array.from(videos).some(video => !video.paused);

        if (anyPlaying) {
            this.pauseAllVideos();
        } else {
            this.playAllVideos();
        }
    }

    async skipPrefix() {
        try {
            const response = await fetch('/api/skip_prefix', { method: 'POST' });
            const result = await response.json();

            if (result.success) {
                this.showMessage('已跳过当前前缀');
                this.loadNextRound();
            } else {
                this.showError('跳过失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        }
    }

    async resetCurrentRound() {
        try {
            const response = await fetch('/api/reset_current');
            const result = await response.json();

            if (result.success) {
                this.showMessage(result.data.message);
                this.loadNextRound();
            } else {
                this.showError('重置失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        }
    }

    async resetAllEvaluation() {
        try {
            const response = await fetch('/api/reset_all');
            const result = await response.json();

            if (result.success) {
                this.showMessage('所有评估已重置');
                location.reload();
            } else {
                this.showError('重置失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        }
    }

    async exportResults() {
        try {
            const response = await fetch('/api/export_results');
            const result = await response.json();

            if (result.success) {
                const dataStr = JSON.stringify(result.data, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `evaluation_results_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                URL.revokeObjectURL(url);
                this.showMessage('结果已导出');
            } else {
                this.showError('导出失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        }
    }

    showConfirmModal(message, action) {
        this.pendingAction = action;
        document.getElementById('confirmMessage').textContent = message;
        document.getElementById('confirmModal').style.display = 'flex';
    }

    hideConfirmModal() {
        document.getElementById('confirmModal').style.display = 'none';
        this.pendingAction = null;
    }

    executeConfirmedAction() {
        if (this.pendingAction) {
            this.pendingAction();
        }
        this.hideConfirmModal();
    }

    showLoading(show) {
        document.getElementById('loadingPanel').style.display = show ? 'block' : 'none';
        document.getElementById('videoGrid').style.display = show ? 'none' : 'grid';
    }

    showError(message) {
        alert('错误: ' + message);
    }

    showMessage(message) {
        // 简单的消息提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            z-index: 1001;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new VideoEvaluator();
});
