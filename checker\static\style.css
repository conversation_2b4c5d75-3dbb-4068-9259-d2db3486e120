/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    text-align: center;
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 20px;
    font-weight: 300;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-weight: 500;
    color: #555;
    min-width: 200px;
    text-align: right;
}

/* 状态面板 */
.status-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.current-info h3 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.3em;
}

.current-info p {
    color: #666;
    font-size: 1.1em;
}

.controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    background: #3498db;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.control-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.skip-btn {
    background: #f39c12;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.skip-btn:hover {
    background: #e67e22;
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.reset-btn {
    background: #e74c3c;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.reset-btn:hover {
    background: #c0392b;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* 视频网格 */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.video-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.video-item video {
    width: 100%;
    height: 250px;
    border-radius: 10px;
    background: #000;
    object-fit: contain;
}

.video-info {
    margin-top: 15px;
    text-align: center;
}

.video-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1.1em;
    word-break: break-all;
}

.select-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    background: #27ae60;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.select-btn:hover {
    background: #229954;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.select-btn:active {
    transform: translateY(0);
}

/* 完成面板 */
.completion-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.completion-panel h2 {
    color: #27ae60;
    font-size: 2.5em;
    margin-bottom: 20px;
}

.completion-panel p {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 25px;
}

.completion-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 25px 0;
}

.completion-stats p {
    margin: 10px 0;
    font-size: 1.1em;
}

/* 加载面板 */
.loading-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 60px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e0e0e0;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.modal-content p {
    color: #666;
    margin-bottom: 25px;
    font-size: 1.1em;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.modal-buttons .control-btn {
    flex: 1;
    max-width: 120px;
}

/* 底部信息 */
footer {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-top: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.help-info h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.help-info ul {
    list-style: none;
    color: #666;
}

.help-info li {
    margin: 8px 0;
    padding-left: 20px;
    position: relative;
}

.help-info li:before {
    content: "•";
    color: #3498db;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .video-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .status-panel {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .controls {
        justify-content: center;
    }
    
    .progress-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .progress-text {
        text-align: center;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .video-item video {
        height: 200px;
    }
    
    .control-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .modal-content {
        padding: 20px;
        margin: 20px;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}
