import os
import shutil

def rename_and_move_videos(source_dirs, target_dir):
    """
    处理指定源文件夹中的MP4视频文件，重命名并移动到目标文件夹。

    Args:
        source_dirs (list): 包含源文件夹名称的列表。
        target_dir (str): 目标文件夹的名称。
    """
    # 1. 验证目标文件夹是否存在，如不存在则创建它
    if not os.path.exists(target_dir):
        try:
            os.makedirs(target_dir)
            print(f"已创建目标文件夹: {target_dir}")
        except OSError as e:
            print(f"创建目标文件夹失败 {target_dir}: {e}")
            return

    # 2. 处理每个源文件夹
    for source_dir in source_dirs:
        if not os.path.isdir(source_dir):
            print(f"源文件夹不存在或不是一个目录: {source_dir}")
            continue

        print(f"正在处理文件夹: {source_dir}")
        for filename in os.listdir(source_dir):
            if filename.endswith(".mp4"):
                source_filepath = os.path.join(source_dir, filename)
                
                try:
                    # 3. 重命名文件
                    name, ext = os.path.splitext(filename)
                    new_filename = f"{name}_{source_dir}{ext}"
                    target_filepath = os.path.join(target_dir, new_filename)

                    # 检查目标文件是否已存在，避免覆盖
                    if os.path.exists(target_filepath):
                        print(f"警告: 目标文件已存在，跳过: {target_filepath}")
                        continue

                    # 4. 移动文件
                    shutil.move(source_filepath, target_filepath)
                    print(f"已重命名并移动: {filename} -> {new_filename} 到 {target_dir}")
                except FileNotFoundError:
                    print(f"错误: 文件未找到 {source_filepath}")
                except PermissionError:
                    print(f"错误: 没有权限访问文件 {source_filepath} 或 {target_filepath}")
                except Exception as e:
                    print(f"处理文件 {filename} 时发生未知错误: {e}")

if __name__ == "__main__":
    source_folders = ['results_seed1_stage2', 'results_seed42_stage2']
    destination_folder = 'checker'
    
    rename_and_move_videos(source_folders, destination_folder)
    print("脚本执行完毕。")
