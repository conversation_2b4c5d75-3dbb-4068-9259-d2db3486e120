import os
import re
from collections import defaultdict

def analyze_video_prefixes(directory, output_file):
    prefix_groups = defaultdict(list)
    
    for root, _, files in os.walk(directory):
        for filename in files:
            if filename.endswith(".mp4"):
                # 提取前缀
                # 示例: frame0_from_02761_NAT2021_train_0428person3_2_0_1_results_seed42_stage2.mp4
                # 期望前缀: frame0_from_02761_NAT2021_train_0428person3_2_0
                
                match = re.search(r'(_\d+)_results_', filename)
                if match:
                    # 找到 _results_ 前的倒数第二个数字下划线分隔符
                    # 例如，对于 _1_results_，我们想要 _1 之前的部分
                    prefix_end_index = filename.rfind(match.group(1), 0, match.start())
                    if prefix_end_index != -1:
                        prefix = filename[:prefix_end_index]
                        prefix_groups[prefix].append(filename)
                    else:
                        # Fallback if the pattern is not exactly as expected, but still contains _results_
                        # This might happen if the number before _results_ is the first number in the name
                        prefix_before_results = filename.split('_results_')[0]
                        # Try to find the last underscore that is followed by a digit
                        last_digit_underscore = -1
                        for i in range(len(prefix_before_results) - 1, -1, -1):
                            if prefix_before_results[i] == '_' and (i + 1 < len(prefix_before_results) and prefix_before_results[i+1].isdigit()):
                                last_digit_underscore = i
                                break
                        if last_digit_underscore != -1:
                            prefix = prefix_before_results[:last_digit_underscore]
                            prefix_groups[prefix].append(filename)
                        else:
                            # If no such pattern, use the whole part before _results_
                            prefix_groups[prefix_before_results].append(filename)
                else:
                    # If _results_ is not found, consider the whole filename (without extension) as prefix
                    prefix = os.path.splitext(filename)[0]
                    prefix_groups[prefix].append(filename)

    with open(output_file, "w", encoding="utf-8") as f:
        f.write(f"总共发现的不同前缀数量: {len(prefix_groups)}\n\n")
        for prefix, files in prefix_groups.items():
            f.write(f"前缀: {prefix}\n")
            for file in files:
                f.write(f"  - {file}\n")
            f.write("\n")

if __name__ == "__main__":
    video_directory = "dataset_drone_video"
    output_report_file = "video_prefix_analysis.txt"
    analyze_video_prefixes(video_directory, output_report_file)
