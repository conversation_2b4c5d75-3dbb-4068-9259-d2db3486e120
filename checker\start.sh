#!/bin/bash

echo "========================================"
echo "视频质量评估工具 - 快速启动"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.7+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "使用Python: $PYTHON_CMD"
$PYTHON_CMD --version

# 检查Flask是否安装
$PYTHON_CMD -c "import flask" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装Flask..."
    pip install flask
    if [ $? -ne 0 ]; then
        echo "错误: Flask安装失败"
        exit 1
    fi
fi

# 检查必要文件
if [ ! -f "../video_prefix_analysis.txt" ]; then
    echo "错误: 未找到video_prefix_analysis.txt文件"
    exit 1
fi

if [ ! -d "../dataset_drone_video" ]; then
    echo "错误: 未找到dataset_drone_video目录"
    exit 1
fi

# 创建Best_video目录
if [ ! -d "../Best_video" ]; then
    echo "创建Best_video目录..."
    mkdir "../Best_video"
fi

echo "环境检查完成，启动Web服务器..."
echo
echo "服务器地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务器"
echo "========================================"
echo

# 启动Flask应用
$PYTHON_CMD app.py
