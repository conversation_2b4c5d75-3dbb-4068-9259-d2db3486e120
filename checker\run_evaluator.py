#!/usr/bin/env python3
"""
视频质量评估工具启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_requirements():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    
    # 检查必要的文件
    required_files = [
        "../video_prefix_analysis.txt",
        "../dataset_drone_video",
        "app.py",
        "video_processor.py",
        "templates/index.html",
        "static/style.css",
        "static/script.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("错误: 缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    # 检查Flask是否安装
    try:
        import flask
        print(f"✓ Flask版本: {flask.__version__}")
    except ImportError:
        print("错误: 未安装Flask")
        print("请运行: pip install flask")
        return False
    
    return True

def get_video_stats():
    """获取视频统计信息"""
    try:
        from video_processor import VideoProcessor
        processor = VideoProcessor(
            "../video_prefix_analysis.txt",
            "../dataset_drone_video",
            "../Best_video"
        )
        
        total_prefixes = len(processor.prefix_groups)
        total_videos = sum(len(videos) for videos in processor.prefix_groups.values())
        
        # 统计视频数量分布
        video_counts = {}
        for videos in processor.prefix_groups.values():
            count = len(videos)
            video_counts[count] = video_counts.get(count, 0) + 1
        
        return {
            "total_prefixes": total_prefixes,
            "total_videos": total_videos,
            "video_counts": video_counts
        }
    except Exception as e:
        print(f"获取统计信息失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("视频质量评估工具")
    print("=" * 60)
    
    # 切换到脚本目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查环境
    if not check_requirements():
        print("\n环境检查失败，请解决上述问题后重试。")
        input("按回车键退出...")
        return
    
    print("✓ 环境检查通过")
    
    # 获取统计信息
    stats = get_video_stats()
    if stats:
        print(f"\n数据统计:")
        print(f"  总前缀数量: {stats['total_prefixes']}")
        print(f"  总视频数量: {stats['total_videos']}")
        print(f"  视频数量分布:")
        for count, prefixes in sorted(stats['video_counts'].items()):
            print(f"    {count}个视频: {prefixes}个前缀")
    
    # 创建必要目录
    os.makedirs("../Best_video", exist_ok=True)
    print("✓ 创建Best_video目录")
    
    print("\n启动Web服务器...")
    print("服务器地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    print("-" * 60)
    
    # 启动Flask应用
    try:
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open("http://localhost:5000")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动Flask应用
        from app import app, init_processor
        init_processor()
        app.run(debug=False, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n服务器已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
