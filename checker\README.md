# 视频质量评估工具

一个用于从多个生成视频中选择最佳版本的Web应用工具。

## 功能特点

- **四视频并排比较**: 同时观看最多4个视频并选择最佳版本
- **淘汰赛机制**: 采用淘汰赛方式，最多进行5轮比较
- **智能处理**: 自动处理特殊情况（单视频自动标记，少于4个视频的组合）
- **进度跟踪**: 实时显示评估进度和状态
- **结果管理**: 自动复制最佳视频到Best_video目录
- **详细日志**: 记录所有评估操作和选择过程
- **响应式设计**: 支持桌面和移动设备

## 系统要求

- Python 3.7+
- Flask
- 现代Web浏览器（支持HTML5视频播放）

## 安装和使用

### 1. 安装依赖

```bash
pip install flask
```

### 2. 准备数据

确保以下文件和目录存在：
- `video_prefix_analysis.txt` - 视频前缀分析文件
- `dataset_drone_video/` - 包含所有视频文件的目录

### 3. 启动应用

#### 方法一：使用启动脚本（推荐）
```bash
cd checker
python run_evaluator.py
```

#### 方法二：直接启动Flask应用
```bash
cd checker
python app.py
```

### 4. 访问Web界面

打开浏览器访问：http://localhost:5000

## 使用说明

### 基本操作

1. **观看视频**: 页面会显示最多4个视频，可以同时播放观看
2. **选择最佳**: 点击视频下方的"选择此视频"按钮选择质量最佳的视频
3. **自动进行**: 系统会自动进行下一轮比较，直到选出最终获胜者
4. **查看进度**: 顶部进度条显示整体评估进度

### 控制功能

- **播放所有**: 同时播放所有视频
- **暂停所有**: 同时暂停所有视频
- **跳过前缀**: 跳过当前前缀的评估
- **重置评估**: 重新开始所有评估（谨慎使用）

### 键盘快捷键

- `1-4`: 选择对应位置的视频
- `空格`: 切换所有视频的播放/暂停状态

## 评估流程

### 淘汰赛算法

1. **单视频**: 自动标记为最佳，无需用户评估
2. **2-4个视频**: 进行一轮比较选出获胜者
3. **5-16个视频**: 进行多轮淘汰赛
   - 第1轮：将视频分组，每组最多4个
   - 每组选出1个获胜者
   - 如果获胜者>4个，继续下一轮
   - 最多进行5轮比较

### 特殊情况处理

- **单个视频**: 自动标记并记录，跳过用户评估
- **不足4个视频**: 显示实际可用数量进行比较
- **文件缺失**: 自动跳过并记录错误

## 结果输出

### Best_video目录

每个前缀的最佳视频会被复制到`Best_video`目录，文件命名格式：
```
{前缀名}_BEST.mp4
```

### 评估日志

系统会生成详细的评估日志：
- `evaluation_state.json` - 当前评估状态
- `evaluation_log.json` - 详细操作日志

### 导出结果

点击"导出结果"按钮可以下载完整的评估报告，包含：
- 评估摘要统计
- 每个前缀的获胜视频
- 详细的操作日志

## 文件结构

```
checker/
├── app.py                    # Flask主应用
├── video_processor.py        # 视频处理核心逻辑
├── run_evaluator.py         # 启动脚本
├── README.md                # 说明文档
├── templates/
│   └── index.html           # 主页面模板
├── static/
│   ├── style.css            # 样式文件
│   └── script.js            # JavaScript逻辑
├── evaluation_state.json    # 评估状态（运行时生成）
└── evaluation_log.json      # 评估日志（运行时生成）
```

## API接口

### GET /api/status
获取当前评估状态

### GET /api/next_round
获取下一轮比较的视频

### POST /api/select_winner
提交获胜视频选择
```json
{
  "winner": "video_filename.mp4"
}
```

### POST /api/skip_prefix
跳过当前前缀

### GET /api/reset
重置评估状态

### GET /api/export_results
导出评估结果

### GET /video/<filename>
获取视频文件

## 故障排除

### 常见问题

1. **视频无法播放**
   - 检查视频文件是否存在
   - 确认浏览器支持MP4格式
   - 检查文件权限

2. **页面加载缓慢**
   - 视频文件较大时加载需要时间
   - 检查网络连接
   - 考虑使用本地服务器

3. **评估状态丢失**
   - 检查`evaluation_state.json`文件
   - 使用重置功能重新开始

### 日志查看

查看详细日志：
```bash
# 查看Flask日志
python app.py

# 查看评估日志
cat evaluation_log.json
```

## 技术架构

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **后端**: Python Flask
- **数据存储**: JSON文件
- **视频处理**: HTML5 Video API

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

MIT License
